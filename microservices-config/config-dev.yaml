service:
  name: ctint-mobile-notification
  basepath: /ctint-mobile-notification
  port: 8207
  version: v1.0.0

logger:
  maxSize: 100
  maxBackups: 10
  maxAge: 28

firebase:
  project_id: "${FIREBASE_PROJECT_ID}"
  credentials_json: "${FIREBASE_CREDENTIALS_JSON}"
  max_retries: 3
  retry_delay: "1s"
  batch_size: 500
  validate_tokens_on_send: true
  timeout_sec: 30

database:
  host: "${DB_HOST:localhost}"
  port: ${DB_PORT:5432}
  name: "${DB_NAME:ctint_notification}"
  username: "${DB_USERNAME:postgres}"
  password: "${DB_PASSWORD:password}"
  max_open_conns: 25
  max_idle_conns: 5
  conn_max_lifetime: "300s"

redis:
  host: "${REDIS_HOST:localhost}"
  port: "${REDIS_PORT:6379}"
  password: "${REDIS_PASSWORD:}"
  db: 0
  pool_size: 10

rate_limiting:
  requests_per_minute: 1000
  requests_per_hour: 50000
  requests_per_day: 1000000
  burst_size: 100

security:
  jwt_secret: "${JWT_SECRET:dev-jwt-secret-key}"
  jwt_expiry: "24h"
  api_key_header: "X-API-Key"

monitoring:
  enable_metrics: true
  metrics_port: 9090
  log_level: "debug"

cors:
  allowed_origins: ["*"]
  allowed_methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
  allowed_headers: ["Content-Type", "Authorization", "X-API-Key"]
