# Firebase Configuration
FIREBASE_PROJECT_ID=your-firebase-project-id
FIREBASE_CREDENTIALS_JSON={\"type\":\"service_account\",\"project_id\":\"your-project\",\"private_key_id\":\"...\"}

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=ctint_notification
DB_USERNAME=postgres
DB_PASSWORD=password

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# Security Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production

# Application Configuration
APP_ENV=development
LOG_LEVEL=debug

# Monitoring Configuration
ENABLE_METRICS=true
METRICS_PORT=9090

# Rate Limiting Configuration
RATE_LIMIT_ENABLED=true
REQUESTS_PER_MINUTE=1000
