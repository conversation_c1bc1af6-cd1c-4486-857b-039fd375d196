package middlewares

import (
	"context"
	"net/http"
	"time"

	"go.uber.org/zap"
)

// APIKey API密钥信息
type APIKey struct {
	Key       string    `json:"key"`
	Name      string    `json:"name"`
	Scopes    []string  `json:"scopes"`
	RateLimit int       `json:"rate_limit"`
	ExpiresAt time.Time `json:"expires_at"`
	CreatedAt time.Time `json:"created_at"`
	Active    bool      `json:"active"`
}

// APIKeyStore API密钥存储接口
type APIKeyStore interface {
	GetAPIKey(key string) (*APIKey, error)
}

// MemoryAPIKeyStore 内存实现的API密钥存储
type MemoryAPIKeyStore struct {
	keys map[string]*APIKey
}

// NewMemoryAPIKeyStore 创建内存API密钥存储
func NewMemoryAPIKeyStore() *MemoryAPIKeyStore {
	store := &MemoryAPIKeyStore{
		keys: make(map[string]*APIKey),
	}
	
	// 添加默认API密钥用于测试
	store.keys["test-api-key-123"] = &APIKey{
		Key:       "test-api-key-123",
		Name:      "Test API Key",
		Scopes:    []string{"notification:send", "notification:read", "device:manage", "topic:manage"},
		RateLimit: 1000,
		ExpiresAt: time.Now().Add(365 * 24 * time.Hour),
		CreatedAt: time.Now(),
		Active:    true,
	}
	
	return store
}

// GetAPIKey 获取API密钥
func (s *MemoryAPIKeyStore) GetAPIKey(key string) (*APIKey, error) {
	apiKey, exists := s.keys[key]
	if !exists {
		return nil, ErrAPIKeyNotFound
	}
	return apiKey, nil
}

// 错误定义
var ErrAPIKeyNotFound = &APIKeyError{Code: "API_KEY_NOT_FOUND", Message: "API key not found"}
var ErrAPIKeyExpired = &APIKeyError{Code: "API_KEY_EXPIRED", Message: "API key has expired"}
var ErrAPIKeyInactive = &APIKeyError{Code: "API_KEY_INACTIVE", Message: "API key is inactive"}

// APIKeyError API密钥错误
type APIKeyError struct {
	Code    string `json:"code"`
	Message string `json:"message"`
}

func (e *APIKeyError) Error() string {
	return e.Message
}

// APIKeyAuthMiddleware API密钥认证中间件
type APIKeyAuthMiddleware struct {
	store      APIKeyStore
	headerName string
	logger     *zap.Logger
}

// NewAPIKeyAuthMiddleware 创建API密钥认证中间件
func NewAPIKeyAuthMiddleware(store APIKeyStore, headerName string, logger *zap.Logger) *APIKeyAuthMiddleware {
	return &APIKeyAuthMiddleware{
		store:      store,
		headerName: headerName,
		logger:     logger,
	}
}

// Middleware 中间件函数
func (m *APIKeyAuthMiddleware) Middleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 从Header获取API密钥
		apiKey := r.Header.Get(m.headerName)
		if apiKey == "" {
			m.writeUnauthorizedResponse(w, "Missing API key")
			return
		}

		// 验证API密钥
		keyInfo, err := m.validateAPIKey(apiKey)
		if err != nil {
			m.logger.Error("API key validation failed", zap.Error(err))
			m.writeUnauthorizedResponse(w, err.Error())
			return
		}

		// 将API密钥信息存储到Context
		ctx := context.WithValue(r.Context(), "api_key", keyInfo)

		// 继续处理请求
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// validateAPIKey 验证API密钥
func (m *APIKeyAuthMiddleware) validateAPIKey(key string) (*APIKey, error) {
	// 从存储中获取API密钥
	apiKey, err := m.store.GetAPIKey(key)
	if err != nil {
		return nil, err
	}

	// 检查密钥是否激活
	if !apiKey.Active {
		return nil, ErrAPIKeyInactive
	}

	// 检查密钥是否过期
	if time.Now().After(apiKey.ExpiresAt) {
		return nil, ErrAPIKeyExpired
	}

	return apiKey, nil
}

// writeUnauthorizedResponse 写入未授权响应
func (m *APIKeyAuthMiddleware) writeUnauthorizedResponse(w http.ResponseWriter, message string) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusUnauthorized)
	w.Write([]byte(`{"success": false, "message": "` + message + `"}`))
}

// GetAPIKeyFromContext 从上下文中获取API密钥信息
func GetAPIKeyFromContext(ctx context.Context) (*APIKey, bool) {
	keyInfo, ok := ctx.Value("api_key").(*APIKey)
	return keyInfo, ok
}