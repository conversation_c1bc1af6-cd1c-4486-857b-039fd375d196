package config

import (
	"ctint-mobile-notification/src/models"
	"fmt"
	"github.com/joho/godotenv"
	"gopkg.in/yaml.v3"
	"log"
	"os"
	"regexp"
	"strings"
)

var ProjectConfig models.ProjectConfig

func InitProjectConfig() {
	env := "dev" // Default to dev if not specified
	if len(os.Args) > 1 {
		env = os.Args[1]
	}

	if env == "dev" {
		_ = godotenv.Load("../.r")
	}

	// Read YAML file based on the environment
	filename := fmt.Sprintf("../microservices-config/config-%s.yaml", env)
	data, err := os.ReadFile(filename)
	if err != nil {
		log.Fatalf("error reading YAML file: %v", err)
	}

	// Unmarshal YAML data into struct
	if err := yaml.Unmarshal(data, &ProjectConfig); err != nil {
		log.Fatalf("error unmarshalling YAML data: %v", err)
	}

	// Access configuration values
	fmt.Printf("Load %s server config successfully\n", env)
}
